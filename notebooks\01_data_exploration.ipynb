{"cells": [{"cell_type": "markdown", "id": "e566b66e", "metadata": {}, "source": ["# 中文酒店评论情感分析 - 数据探索\n", "\n", "本笔记本将对ChnSentiCorp酒店评论数据集进行全面的探索性数据分析（EDA）。"]}, {"cell_type": "code", "execution_count": null, "id": "393e370f", "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import jieba\n", "from collections import Counter\n", "import re\n", "from wordcloud import WordCloud\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False"]}, {"cell_type": "markdown", "id": "c7190ee4", "metadata": {}, "source": ["## 1. 数据加载与基本信息"]}, {"cell_type": "code", "execution_count": null, "id": "b8c674d9", "metadata": {}, "outputs": [], "source": ["# 加载数据\n", "df = pd.read_csv('../ChnSentiCorp_htl_all.csv', encoding='utf-8')\n", "\n", "print(f\"数据集形状: {df.shape}\")\n", "print(f\"\\n数据集基本信息:\")\n", "print(df.info())\n", "\n", "print(f\"\\n数据集前5行:\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a7ac1046", "metadata": {}, "outputs": [], "source": ["# 检查缺失值\n", "print(\"缺失值统计:\")\n", "print(df.isnull().sum())\n", "\n", "print(f\"\\n数据类型:\")\n", "print(df.dtypes)\n", "\n", "print(f\"\\n数据集描述性统计:\")\n", "df.describe(include='all')"]}, {"cell_type": "markdown", "id": "a14622db", "metadata": {}, "source": ["## 2. 标签分布分析"]}, {"cell_type": "code", "execution_count": null, "id": "5062ba07", "metadata": {}, "outputs": [], "source": ["# 标签分布\n", "label_counts = df['label'].value_counts()\n", "print(\"标签分布:\")\n", "print(label_counts)\n", "print(f\"\\n标签比例:\")\n", "print(df['label'].value_counts(normalize=True))\n", "\n", "# 可视化标签分布\n", "fig, axes = plt.subplots(1, 2, figsize=(12, 5))\n", "\n", "# 饼图\n", "labels = ['正面', '负面']\n", "axes[0].pie(label_counts.values, labels=labels, autopct='%1.1f%%', startangle=90)\n", "axes[0].set_title('情感标签分布 - 饼图')\n", "\n", "# 柱状图\n", "sns.countplot(data=df, x='label', ax=axes[1])\n", "axes[1].set_title('情感标签分布 - 柱状图')\n", "axes[1].set_xlabel('标签 (0: 负面, 1: 正面)')\n", "axes[1].set_ylabel('数量')\n", "\n", "# 添加数值标签\n", "for i, v in enumerate(label_counts.values):\n", "    axes[1].text(i, v + 50, str(v), ha='center', va='bottom')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "6d6f32cd", "metadata": {}, "source": ["## 3. 文本长度分析"]}, {"cell_type": "code", "execution_count": null, "id": "68cbb479", "metadata": {}, "outputs": [], "source": ["# 计算文本长度\n", "df['text_length'] = df['review'].astype(str).apply(len)\n", "df['word_count'] = df['review'].astype(str).apply(lambda x: len(x.split()))\n", "\n", "# 文本长度统计\n", "print(\"文本长度统计:\")\n", "print(df['text_length'].describe())\n", "\n", "# 可视化文本长度分布\n", "fig, axes = plt.subplots(1, 3, figsize=(18, 6))\n", "\n", "# 整体文本长度分布\n", "axes[0].hist(df['text_length'], bins=50, alpha=0.7, edgecolor='black')\n", "axes[0].set_title('文本长度分布')\n", "axes[0].set_xlabel('字符数')\n", "axes[0].set_ylabel('频次')\n", "\n", "# 按标签分组的文本长度分布\n", "for label in [0, 1]:\n", "    subset = df[df['label'] == label]['text_length']\n", "    axes[1].hist(subset, bins=30, alpha=0.6, \n", "                   label=f'标签 {label} ({\"负面\" if label == 0 else \"正面\"})')\n", "axes[1].set_title('按情感标签分组的文本长度分布')\n", "axes[1].set_xlabel('字符数')\n", "axes[1].set_ylabel('频次')\n", "axes[1].legend()\n", "\n", "# 文本长度箱线图\n", "sns.boxplot(data=df, x='label', y='text_length', ax=axes[2])\n", "axes[2].set_title('按标签分组的文本长度箱线图')\n", "axes[2].set_xlabel('标签 (0: 负面, 1: 正面)')\n", "axes[2].set_ylabel('字符数')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "b425eb18", "metadata": {}, "outputs": [], "source": ["# 统计不同长度区间的文本数量\n", "length_bins = [0, 50, 100, 200, 500, 1000, float('inf')]\n", "length_labels = ['0-50', '51-100', '101-200', '201-500', '501-1000', '1000+']\n", "\n", "df['length_category'] = pd.cut(df['text_length'], bins=length_bins, labels=length_labels)\n", "\n", "print(\"不同长度区间的文本分布:\")\n", "length_dist = df['length_category'].value_counts().sort_index()\n", "print(length_dist)\n", "\n", "# 可视化\n", "plt.figure(figsize=(10, 6))\n", "length_dist.plot(kind='bar')\n", "plt.title('不同长度区间的文本数量分布')\n", "plt.xlabel('文本长度区间')\n", "plt.ylabel('数量')\n", "plt.xticks(rotation=45)\n", "\n", "# 添加数值标签\n", "for i, v in enumerate(length_dist.values):\n", "    plt.text(i, v + 20, str(v), ha='center', va='bottom')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "7c6f88a9", "metadata": {}, "source": ["## 4. 文本内容分析"]}, {"cell_type": "code", "execution_count": null, "id": "7b4e31ad", "metadata": {}, "outputs": [], "source": ["# 随机展示一些评论样本\n", "print(\"=== 正面评论样本 ===\")\n", "positive_samples = df[df['label'] == 1]['review'].sample(5)\n", "for i, review in enumerate(positive_samples, 1):\n", "    print(f\"{i}. {review}\")\n", "    print()\n", "\n", "print(\"\\n=== 负面评论样本 ===\")\n", "negative_samples = df[df['label'] == 0]['review'].sample(5)\n", "for i, review in enumerate(negative_samples, 1):\n", "    print(f\"{i}. {review}\")\n", "    print()"]}, {"cell_type": "code", "execution_count": null, "id": "da429fe1", "metadata": {}, "outputs": [], "source": ["# 分词和词频统计\n", "def segment_text(text):\n", "    \"\"\"中文分词\"\"\"\n", "    if pd.isna(text):\n", "        return []\n", "    # 清理文本\n", "    text = re.sub(r'[^\\u4e00-\\u9fa5a-zA-Z0-9]', ' ', str(text))\n", "    # 分词\n", "    words = jieba.cut(text)\n", "    # 过滤短词和停用词\n", "    words = [word.strip() for word in words if len(word.strip()) > 1]\n", "    return words\n", "\n", "# 对所有文本进行分词\n", "print(\"正在进行中文分词...\")\n", "df['words'] = df['review'].apply(segment_text)\n", "\n", "# 统计总词频\n", "all_words = []\n", "for words in df['words']:\n", "    all_words.extend(words)\n", "\n", "word_freq = Counter(all_words)\n", "print(f\"总词汇数: {len(word_freq)}\")\n", "print(f\"总词次数: {sum(word_freq.values())}\")\n", "\n", "print(\"\\n最常见的20个词:\")\n", "for word, freq in word_freq.most_common(20):\n", "    print(f\"{word}: {freq}\")"]}, {"cell_type": "code", "execution_count": null, "id": "bab30146", "metadata": {}, "outputs": [], "source": ["# 按情感标签分别统计词频\n", "positive_words = []\n", "negative_words = []\n", "\n", "for idx, words in enumerate(df['words']):\n", "    if df.iloc[idx]['label'] == 1:\n", "        positive_words.extend(words)\n", "    else:\n", "        negative_words.extend(words)\n", "\n", "positive_freq = Counter(positive_words)\n", "negative_freq = Counter(negative_words)\n", "\n", "print(\"正面评论中最常见的15个词:\")\n", "for word, freq in positive_freq.most_common(15):\n", "    print(f\"{word}: {freq}\")\n", "\n", "print(\"\\n负面评论中最常见的15个词:\")\n", "for word, freq in negative_freq.most_common(15):\n", "    print(f\"{word}: {freq}\")"]}, {"cell_type": "code", "execution_count": null, "id": "a56901df", "metadata": {}, "outputs": [], "source": ["# 可视化词频分布\n", "fig, axes = plt.subplots(1, 2, figsize=(16, 6))\n", "\n", "# 正面词频\n", "pos_words, pos_freqs = zip(*positive_freq.most_common(15))\n", "axes[0].barh(range(len(pos_words)), pos_freqs)\n", "axes[0].set_yticks(range(len(pos_words)))\n", "axes[0].set_yticklabels(pos_words)\n", "axes[0].set_title('正面评论高频词')\n", "axes[0].set_xlabel('频次')\n", "axes[0].invert_yaxis()\n", "\n", "# 负面词频\n", "neg_words, neg_freqs = zip(*negative_freq.most_common(15))\n", "axes[1].barh(range(len(neg_words)), neg_freqs, color='red', alpha=0.7)\n", "axes[1].set_yticks(range(len(neg_words)))\n", "axes[1].set_yticklabels(neg_words)\n", "axes[1].set_title('负面评论高频词')\n", "axes[1].set_xlabel('频次')\n", "axes[1].invert_yaxis()\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "df070783", "metadata": {}, "source": ["## 5. 词云可视化"]}, {"cell_type": "code", "execution_count": null, "id": "dff7c78a", "metadata": {}, "outputs": [], "source": ["# 生成词云\n", "# 注意：需要先安装wordcloud库\n", "# pip install wordcloud\n", "\n", "import os\n", "import platform\n", "\n", "try:\n", "    from wordcloud import WordCloud\n", "    \n", "    # 设置中文字体路径\n", "    def get_chinese_font():\n", "        \"\"\"获取系统中文字体路径\"\"\"\n", "        system = platform.system()\n", "        if system == \"Windows\":\n", "            font_paths = [\n", "                \"C:/Windows/Fonts/simhei.ttf\",  # 黑体\n", "                \"C:/Windows/Fonts/msyh.ttc\",    # 微软雅黑\n", "                \"C:/Windows/Fonts/simsun.ttc\",  # 宋体\n", "            ]\n", "        elif system == \"Darwin\":  # macOS\n", "            font_paths = [\n", "                \"/System/Library/Fonts/PingFang.ttc\",\n", "                \"/System/Library/Fonts/STHeiti Light.ttc\",\n", "            ]\n", "        else:  # Linux\n", "            font_paths = [\n", "                \"/usr/share/fonts/truetype/wqy/wqy-microhei.ttc\",\n", "                \"/usr/share/fonts/truetype/arphic/ukai.ttc\",\n", "            ]\n", "        \n", "        # 找到第一个存在的字体文件\n", "        for font_path in font_paths:\n", "            if os.path.exists(font_path):\n", "                return font_path\n", "        return None\n", "    \n", "    chinese_font = get_chinese_font()\n", "    print(f\"使用字体: {chinese_font}\")\n", "    \n", "    # 正面评论词云\n", "    positive_text = ' '.join(positive_words)\n", "    wordcloud_pos = WordCloud(\n", "        font_path=chinese_font,\n", "        width=800, height=400,\n", "        background_color='white',\n", "        max_words=100,\n", "        colormap='Blues',\n", "        collocations=False,  # 避免重复词组\n", "        relative_scaling=0.5,\n", "        random_state=42\n", "    ).generate(positive_text)\n", "    \n", "    # 负面评论词云\n", "    negative_text = ' '.join(negative_words)\n", "    wordcloud_neg = WordCloud(\n", "        font_path=chinese_font,\n", "        width=800, height=400,\n", "        background_color='white',\n", "        max_words=100,\n", "        colormap='Reds',\n", "        collocations=False,  # 避免重复词组\n", "        relative_scaling=0.5,\n", "        random_state=42\n", "    ).generate(negative_text)\n", "    \n", "    # 显示词云\n", "    fig, axes = plt.subplots(1, 2, figsize=(16, 8))\n", "    \n", "    axes[0].imshow(wordcloud_pos, interpolation='bilinear')\n", "    axes[0].axis('off')\n", "    axes[0].set_title('正面评论词云', fontsize=16)\n", "    \n", "    axes[1].imshow(wordcloud_neg, interpolation='bilinear')\n", "    axes[1].axis('off')\n", "    axes[1].set_title('负面评论词云', fontsize=16)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # 保存词云图片\n", "    try:\n", "        os.makedirs('../results', exist_ok=True)\n", "        fig.savefig('../results/wordcloud_analysis.png', dpi=300, bbox_inches='tight')\n", "        print(\"词云图片已保存到 ../results/wordcloud_analysis.png\")\n", "    except Exception as e:\n", "        print(f\"保存图片时出错: {e}\")\n", "    \n", "except ImportError:\n", "    print(\"请安装wordcloud库：pip install wordcloud\")\n", "except Exception as e:\n", "    print(f\"生成词云时出错: {e}\")\n", "    print(\"如果遇到字体问题，请确保系统中安装了中文字体\")"]}, {"cell_type": "markdown", "id": "58b8a0e7", "metadata": {}, "source": ["## 6. 特殊字符和模式分析"]}, {"cell_type": "code", "execution_count": null, "id": "a3bcf276", "metadata": {}, "outputs": [], "source": ["# 分析特殊字符和模式\n", "def analyze_text_patterns(text):\n", "    \"\"\"分析文本模式\"\"\"\n", "    if pd.isna(text):\n", "        return {\n", "            'has_emoji': <PERSON><PERSON><PERSON>,\n", "            'has_punctuation': <PERSON><PERSON><PERSON>,\n", "            'has_english': <PERSON><PERSON><PERSON>,\n", "            'has_numbers': <PERSON><PERSON><PERSON>,\n", "            'exclamation_count': 0,\n", "            'question_count': 0\n", "        }\n", "    \n", "    text = str(text)\n", "    \n", "    return {\n", "        'has_emoji': bool(re.search(r'[😀-🙏]', text)),\n", "        'has_punctuation': bool(re.search(r'[！？。，；：]', text)),\n", "        'has_english': bool(re.search(r'[a-zA-Z]', text)),\n", "        'has_numbers': bool(re.search(r'\\d', text)),\n", "        'exclamation_count': text.count('！') + text.count('!'),\n", "        'question_count': text.count('？') + text.count('?')\n", "    }\n", "\n", "# 应用模式分析\n", "pattern_analysis = df['review'].apply(analyze_text_patterns)\n", "pattern_df = pd.DataFrame(pattern_analysis.tolist())\n", "\n", "# 合并到原数据框\n", "df = pd.concat([df, pattern_df], axis=1)\n", "\n", "# 统计各种模式的出现频率\n", "print(\"文本模式统计:\")\n", "print(f\"包含表情符号: {df['has_emoji'].sum()} ({df['has_emoji'].mean():.2%})\")\n", "print(f\"包含标点符号: {df['has_punctuation'].sum()} ({df['has_punctuation'].mean():.2%})\")\n", "print(f\"包含英文字符: {df['has_english'].sum()} ({df['has_english'].mean():.2%})\")\n", "print(f\"包含数字: {df['has_numbers'].sum()} ({df['has_numbers'].mean():.2%})\")\n", "print(f\"平均感叹号数量: {df['exclamation_count'].mean():.2f}\")\n", "print(f\"平均问号数量: {df['question_count'].mean():.2f}\")"]}, {"cell_type": "code", "execution_count": null, "id": "19dfe67f", "metadata": {}, "outputs": [], "source": ["# 按情感标签分析文本模式\n", "print(\"按情感标签分组的文本模式分析:\")\n", "for label in [0, 1]:\n", "    subset = df[df['label'] == label]\n", "    label_name = \"负面\" if label == 0 else \"正面\"\n", "    print(f\"\\n{label_name}评论:\")\n", "    print(f\"  包含标点符号: {subset['has_punctuation'].mean():.2%}\")\n", "    print(f\"  包含英文字符: {subset['has_english'].mean():.2%}\")\n", "    print(f\"  包含数字: {subset['has_numbers'].mean():.2%}\")\n", "    print(f\"  平均感叹号数量: {subset['exclamation_count'].mean():.2f}\")\n", "    print(f\"  平均问号数量: {subset['question_count'].mean():.2f}\")\n", "    print(f\"  平均文本长度: {subset['text_length'].mean():.1f}\")"]}, {"cell_type": "markdown", "id": "21f18850", "metadata": {}, "source": ["## 7. 数据质量评估"]}, {"cell_type": "code", "execution_count": null, "id": "18731f46", "metadata": {}, "outputs": [], "source": ["# 检查数据质量问题\n", "print(\"数据质量评估:\")\n", "\n", "# 1. 重复评论\n", "duplicate_reviews = df['review'].duplicated().sum()\n", "print(f\"重复评论数量: {duplicate_reviews}\")\n", "\n", "# 2. 过短评论\n", "very_short = (df['text_length'] < 10).sum()\n", "print(f\"过短评论（<10字符）数量: {very_short}\")\n", "\n", "# 3. 过长评论\n", "very_long = (df['text_length'] > 1000).sum()\n", "print(f\"过长评论（>1000字符）数量: {very_long}\")\n", "\n", "# 4. 空白或无意义评论\n", "empty_or_meaningless = df['review'].astype(str).str.strip().str.len() == 0\n", "print(f\"空白评论数量: {empty_or_meaningless.sum()}\")\n", "\n", "# 展示一些问题数据样本\n", "if duplicate_reviews > 0:\n", "    print(\"\\n重复评论示例:\")\n", "    duplicates = df[df['review'].duplicated(keep=False)]['review'].unique()[:3]\n", "    for i, dup in enumerate(duplicates, 1):\n", "        print(f\"{i}. {dup}\")\n", "\n", "if very_short > 0:\n", "    print(\"\\n过短评论示例:\")\n", "    short_reviews = df[df['text_length'] < 10]['review'].head(3)\n", "    for i, review in enumerate(short_reviews, 1):\n", "        print(f\"{i}. {review}\")"]}, {"cell_type": "markdown", "id": "24392a27", "metadata": {}, "source": ["## 8. 总结和建议"]}, {"cell_type": "code", "execution_count": null, "id": "22fabbd5", "metadata": {}, "outputs": [], "source": ["# 数据集总结\n", "print(\"=== 数据集总结 ===\")\n", "print(f\"数据集大小: {len(df):,} 条评论\")\n", "print(f\"正面评论: {(df['label'] == 1).sum():,} 条 ({(df['label'] == 1).mean():.1%})\")\n", "print(f\"负面评论: {(df['label'] == 0).sum():,} 条 ({(df['label'] == 0).mean():.1%})\")\n", "print(f\"平均文本长度: {df['text_length'].mean():.1f} 字符\")\n", "print(f\"文本长度中位数: {df['text_length'].median():.1f} 字符\")\n", "print(f\"词汇总数: {len(word_freq):,} 个不同词汇\")\n", "\n", "print(\"\\n=== 数据预处理建议 ===\")\n", "print(\"1. 数据基本平衡，无需特殊采样处理\")\n", "print(\"2. 文本长度差异较大，建议设置合适的最大长度截断\")\n", "print(\"3. 包含标点符号和数字，需要考虑是否保留\")\n", "print(\"4. 建议移除重复评论和过短评论\")\n", "print(\"5. 可以考虑使用分词结果辅助BERT模型\")\n", "\n", "print(\"\\n=== 建议的数据预处理参数 ===\")\n", "print(f\"推荐最大序列长度: {int(df['text_length'].quantile(0.95))} (覆盖95%数据)\")\n", "print(f\"最小文本长度阈值: 10 字符\")\n", "print(f\"批处理大小建议: 16-32\")"]}, {"cell_type": "code", "execution_count": null, "id": "a46ce6ea", "metadata": {}, "outputs": [], "source": ["# 保存分析结果\n", "analysis_summary = {\n", "    'dataset_size': len(df),\n", "    'positive_count': int((df['label'] == 1).sum()),\n", "    'negative_count': int((df['label'] == 0).sum()),\n", "    'avg_text_length': float(df['text_length'].mean()),\n", "    'median_text_length': float(df['text_length'].median()),\n", "    'vocabulary_size': len(word_freq),\n", "    'duplicate_count': int(duplicate_reviews),\n", "    'short_text_count': int(very_short),\n", "    'long_text_count': int(very_long),\n", "    'recommended_max_length': int(df['text_length'].quantile(0.95)),\n", "    'top_positive_words': positive_freq.most_common(20),\n", "    'top_negative_words': negative_freq.most_common(20)\n", "}\n", "\n", "# 保存到文件\n", "import json\n", "with open('../data/analysis/eda_summary.json', 'w', encoding='utf-8') as f:\n", "    json.dump(analysis_summary, f, ensure_ascii=False, indent=2)\n", "\n", "print(\"分析结果已保存到 ../data/analysis/eda_summary.json\")"]}], "metadata": {"kernelspec": {"display_name": "bert_sentiment_analysis", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}