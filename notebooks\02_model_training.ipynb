{"cells": [{"cell_type": "markdown", "id": "f41f7030", "metadata": {}, "source": ["# 中文酒店评论情感分析 - 模型训练\n", "\n", "本笔记本将训练和比较基础BERT模型和改进的BERT+BiLSTM+Attention模型。"]}, {"cell_type": "code", "execution_count": null, "id": "982822f4", "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import sys\n", "import os\n", "sys.path.append('..')\n", "\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from tqdm import tqdm\n", "import json\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 导入自定义模块\n", "from src.data_preprocessing import DataPreprocessor\n", "from models.base_model import create_base_model\n", "from models.improved_model import create_improved_model, ContrastiveLoss\n", "from src.model_training import ModelTrainer\n", "from src.utils import plot_training_history, set_seed\n", "\n", "# 设置随机种子\n", "set_seed(42)\n", "\n", "# 设置设备\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"使用设备: {device}\")\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False"]}, {"cell_type": "markdown", "id": "e75df9b3", "metadata": {}, "source": ["## 1. 数据准备"]}, {"cell_type": "code", "execution_count": null, "id": "b3aafa08", "metadata": {}, "outputs": [], "source": ["# 初始化数据预处理器\n", "preprocessor = DataPreprocessor('../ChnSentiCorp_htl_all.csv')\n", "\n", "# 检查是否已有处理后的数据\n", "if not os.path.exists('../data/processed/train.csv'):\n", "    print(\"开始数据预处理...\")\n", "    train_df, val_df, test_df = preprocessor.preprocess_data()\n", "    print(\"数据预处理完成！\")\n", "else:\n", "    print(\"发现已处理的数据，直接加载...\")\n", "    train_df = pd.read_csv('../data/processed/train.csv')\n", "    val_df = pd.read_csv('../data/processed/val.csv')\n", "    test_df = pd.read_csv('../data/processed/test.csv')\n", "\n", "print(f\"训练集大小: {len(train_df)}\")\n", "print(f\"验证集大小: {len(val_df)}\")\n", "print(f\"测试集大小: {len(test_df)}\")\n", "\n", "# 创建数据加载器\n", "batch_size = 16\n", "train_loader, val_loader, test_loader = preprocessor.create_dataloaders(batch_size=batch_size)\n", "\n", "print(f\"\\n数据加载器创建完成:\")\n", "print(f\"训练批次数: {len(train_loader)}\")\n", "print(f\"验证批次数: {len(val_loader)}\")\n", "print(f\"测试批次数: {len(test_loader)}\")"]}, {"cell_type": "markdown", "id": "028caee7", "metadata": {}, "source": ["## 2. 模型架构对比"]}, {"cell_type": "code", "execution_count": null, "id": "1663ad00", "metadata": {}, "outputs": [], "source": ["# 创建基础BERT模型\n", "base_model = create_base_model(model_type='enhanced')\n", "base_model.to(device)\n", "\n", "# 创建改进模型\n", "improved_model = create_improved_model(use_contrastive_loss=True)\n", "improved_model.to(device)\n", "\n", "# 计算参数数量\n", "def count_parameters(model):\n", "    return sum(p.numel() for p in model.parameters() if p.requires_grad)\n", "\n", "base_params = count_parameters(base_model)\n", "improved_params = count_parameters(improved_model)\n", "\n", "print(\"模型参数对比:\")\n", "print(f\"基础BERT模型参数量: {base_params:,}\")\n", "print(f\"改进模型参数量: {improved_params:,}\")\n", "print(f\"参数增长: {improved_params - base_params:,} ({(improved_params/base_params - 1)*100:.1f}%)\")\n", "\n", "# 测试前向传播\n", "sample_batch = next(iter(train_loader))\n", "input_ids = sample_batch['input_ids'][:2].to(device)\n", "attention_mask = sample_batch['attention_mask'][:2].to(device)\n", "\n", "print(\"\\n测试前向传播:\")\n", "with torch.no_grad():\n", "    base_output = base_model(input_ids, attention_mask)\n", "    improved_output = improved_model(input_ids, attention_mask)\n", "    \n", "    print(f\"基础模型输出形状: {base_output['logits'].shape}\")\n", "    print(f\"改进模型输出形状: {improved_output['logits'].shape}\")\n", "    \n", "    if 'attention_weights' in improved_output:\n", "        print(f\"注意力权重形状: {improved_output['attention_weights'].shape}\")\n", "    \n", "    if 'projection' in improved_output:\n", "        print(f\"对比学习投影形状: {improved_output['projection'].shape}\")"]}, {"cell_type": "markdown", "id": "2e7082c5", "metadata": {}, "source": ["## 3. 训练基础BERT模型"]}, {"cell_type": "code", "execution_count": null, "id": "72e20a49", "metadata": {}, "outputs": [], "source": ["# 配置训练参数\n", "training_config = {\n", "    'learning_rate': 2e-5,\n", "    'batch_size': batch_size,\n", "    'max_epochs': 5,  # 为了演示，使用较少的epoch\n", "    'patience': 3,\n", "    'save_dir': '../models/saved_models'\n", "}\n", "\n", "# 清除GPU缓存\n", "if torch.cuda.is_available():\n", "    torch.cuda.empty_cache()\n", "    print(\"GPU缓存已清除\")\n", "\n", "print(\"开始训练基础BERT模型...\")\n", "\n", "# 创建训练器\n", "base_trainer = ModelTrainer(\n", "    model_type='base',\n", "    device=device,\n", "    **training_config\n", ")\n", "\n", "# 训练模型\n", "base_history = base_trainer.train(train_loader, val_loader)\n", "\n", "print(\"基础BERT模型训练完成！\")"]}, {"cell_type": "code", "execution_count": null, "id": "ecf207e4", "metadata": {}, "outputs": [], "source": ["# 可视化基础模型训练历史\n", "plot_training_history(base_history, save_path='../results/base_model_training_history.png')"]}, {"cell_type": "markdown", "id": "c4d4b7a5", "metadata": {}, "source": ["## 4. 训练改进模型"]}, {"cell_type": "code", "execution_count": null, "id": "6e527a36", "metadata": {}, "outputs": [], "source": ["# 确保训练配置已定义\n", "training_config = {\n", "    'learning_rate': 2e-5,\n", "    'batch_size': 16,  # 使用已定义的batch_size\n", "    'max_epochs': 5,  # 为了演示，使用较少的epoch\n", "    'patience': 3,\n", "    'save_dir': '../models/saved_models'\n", "}\n", "\n", "# 清除GPU缓存\n", "if torch.cuda.is_available():\n", "    torch.cuda.empty_cache()\n", "    print(\"GPU缓存已清除\")\n", "\n", "print(\"开始训练改进模型...\")\n", "\n", "# 创建改进模型训练器\n", "improved_trainer = ModelTrainer(\n", "    model_type='improved',\n", "    device=device,\n", "    use_contrastive_loss=True,\n", "    contrastive_weight=0.1,\n", "    **training_config\n", ")\n", "\n", "# 训练模型\n", "improved_history = improved_trainer.train(train_loader, val_loader)\n", "\n", "print(\"改进模型训练完成！\")"]}, {"cell_type": "code", "execution_count": null, "id": "9117f46d", "metadata": {}, "outputs": [], "source": ["# 启用异常检测来捕获梯度问题\n", "torch.autograd.set_detect_anomaly(True)\n", "print(\"已启用梯度异常检测\")"]}, {"cell_type": "code", "execution_count": null, "id": "7405b69e", "metadata": {}, "outputs": [], "source": ["# 可视化改进模型训练历史\n", "plot_training_history(improved_history, save_path='../results/improved_model_training_history.png')"]}, {"cell_type": "markdown", "id": "934ee40d", "metadata": {}, "source": ["## 5. 训练结果对比"]}, {"cell_type": "code", "execution_count": null, "id": "3ba831ff", "metadata": {}, "outputs": [], "source": ["# 对比两个模型的训练结果\n", "def compare_training_results(base_hist, improved_hist):\n", "    \"\"\"对比训练结果\"\"\"\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "    \n", "    # 训练损失对比\n", "    axes[0, 0].plot(base_hist['train_loss'], label='基础BERT', marker='o')\n", "    axes[0, 0].plot(improved_hist['train_loss'], label='改进模型', marker='s')\n", "    axes[0, 0].set_title('训练损失对比')\n", "    axes[0, 0].set_xlabel('Epoch')\n", "    axes[0, 0].set_ylabel('Loss')\n", "    axes[0, 0].legend()\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "    \n", "    # 验证损失对比\n", "    axes[0, 1].plot(base_hist['val_loss'], label='基础BERT', marker='o')\n", "    axes[0, 1].plot(improved_hist['val_loss'], label='改进模型', marker='s')\n", "    axes[0, 1].set_title('验证损失对比')\n", "    axes[0, 1].set_xlabel('Epoch')\n", "    axes[0, 1].set_ylabel('Loss')\n", "    axes[0, 1].legend()\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "    \n", "    # 训练准确率对比\n", "    axes[1, 0].plot(base_hist['train_acc'], label='基础BERT', marker='o')\n", "    axes[1, 0].plot(improved_hist['train_acc'], label='改进模型', marker='s')\n", "    axes[1, 0].set_title('训练准确率对比')\n", "    axes[1, 0].set_xlabel('Epoch')\n", "    axes[1, 0].set_ylabel('Accuracy')\n", "    axes[1, 0].legend()\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "    \n", "    # 验证准确率对比\n", "    axes[1, 1].plot(base_hist['val_acc'], label='基础BERT', marker='o')\n", "    axes[1, 1].plot(improved_hist['val_acc'], label='改进模型', marker='s')\n", "    axes[1, 1].set_title('验证准确率对比')\n", "    axes[1, 1].set_xlabel('Epoch')\n", "    axes[1, 1].set_ylabel('Accuracy')\n", "    axes[1, 1].legend()\n", "    axes[1, 1].grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.savefig('../results/model_comparison_training.png', dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "\n", "compare_training_results(base_history, improved_history)"]}, {"cell_type": "code", "execution_count": null, "id": "4bb3bc84", "metadata": {}, "outputs": [], "source": ["# 数值对比\n", "def print_final_metrics(base_hist, improved_hist):\n", "    \"\"\"打印最终指标对比\"\"\"\n", "    print(\"=== 最终训练指标对比 ===\")\n", "    print(f\"{'指标':<15} {'基础BERT':<12} {'改进模型':<12} {'提升':<10}\")\n", "    print(\"-\" * 55)\n", "    \n", "    base_final = {\n", "        'train_loss': base_hist['train_loss'][-1],\n", "        'val_loss': base_hist['val_loss'][-1],\n", "        'train_acc': base_hist['train_acc'][-1],\n", "        'val_acc': base_hist['val_acc'][-1]\n", "    }\n", "    \n", "    improved_final = {\n", "        'train_loss': improved_hist['train_loss'][-1],\n", "        'val_loss': improved_hist['val_loss'][-1],\n", "        'train_acc': improved_hist['train_acc'][-1],\n", "        'val_acc': improved_hist['val_acc'][-1]\n", "    }\n", "    \n", "    metrics_names = {\n", "        'train_loss': '训练损失',\n", "        'val_loss': '验证损失',\n", "        'train_acc': '训练准确率',\n", "        'val_acc': '验证准确率'\n", "    }\n", "    \n", "    for key in base_final:\n", "        base_val = base_final[key]\n", "        improved_val = improved_final[key]\n", "        \n", "        if 'loss' in key:\n", "            improvement = (base_val - improved_val) / base_val * 100\n", "            improvement_str = f\"{improvement:+.1f}%\"\n", "        else:\n", "            improvement = (improved_val - base_val) / base_val * 100\n", "            improvement_str = f\"{improvement:+.1f}%\"\n", "        \n", "        print(f\"{metrics_names[key]:<15} {base_val:<12.4f} {improved_val:<12.4f} {improvement_str:<10}\")\n", "\n", "print_final_metrics(base_history, improved_history)"]}, {"cell_type": "markdown", "id": "564d4047", "metadata": {}, "source": ["## 6. 模型复杂度分析"]}, {"cell_type": "code", "execution_count": null, "id": "e1c22556", "metadata": {}, "outputs": [], "source": ["# 分析模型的计算复杂度\n", "import time\n", "\n", "def measure_inference_time(model, data_loader, device, num_batches=10):\n", "    \"\"\"测量推理时间\"\"\"\n", "    model.eval()\n", "    times = []\n", "    \n", "    with torch.no_grad():\n", "        for i, batch in enumerate(data_loader):\n", "            if i >= num_batches:\n", "                break\n", "                \n", "            input_ids = batch['input_ids'].to(device)\n", "            attention_mask = batch['attention_mask'].to(device)\n", "            \n", "            start_time = time.time()\n", "            outputs = model(input_ids, attention_mask)\n", "            torch.cuda.synchronize() if device.type == 'cuda' else None\n", "            end_time = time.time()\n", "            \n", "            times.append(end_time - start_time)\n", "    \n", "    return np.mean(times), np.std(times)\n", "\n", "# 测量推理时间\n", "print(\"测量模型推理时间...\")\n", "\n", "# 基础模型\n", "base_trainer.model.eval()\n", "base_mean_time, base_std_time = measure_inference_time(\n", "    base_trainer.model, val_loader, device\n", ")\n", "\n", "# 改进模型\n", "improved_trainer.model.eval()\n", "improved_mean_time, improved_std_time = measure_inference_time(\n", "    improved_trainer.model, val_loader, device\n", ")\n", "\n", "print(f\"\\n推理时间对比（每批次）:\")\n", "print(f\"基础BERT模型: {base_mean_time:.4f} ± {base_std_time:.4f} 秒\")\n", "print(f\"改进模型: {improved_mean_time:.4f} ± {improved_std_time:.4f} 秒\")\n", "print(f\"时间增长: {(improved_mean_time/base_mean_time - 1)*100:.1f}%\")\n", "\n", "# 计算每个样本的推理时间\n", "base_per_sample = base_mean_time / batch_size\n", "improved_per_sample = improved_mean_time / batch_size\n", "\n", "print(f\"\\n每个样本推理时间:\")\n", "print(f\"基础BERT模型: {base_per_sample*1000:.2f} 毫秒\")\n", "print(f\"改进模型: {improved_per_sample*1000:.2f} 毫秒\")"]}, {"cell_type": "markdown", "id": "e9a6065c", "metadata": {}, "source": ["## 7. 保存训练结果"]}, {"cell_type": "code", "execution_count": null, "id": "632758b5", "metadata": {}, "outputs": [], "source": ["# 保存训练总结\n", "training_summary = {\n", "    'experiment_config': training_config,\n", "    'model_comparison': {\n", "        'base_model': {\n", "            'parameters': int(base_params),\n", "            'final_metrics': {\n", "                'train_loss': float(base_history['train_loss'][-1]),\n", "                'val_loss': float(base_history['val_loss'][-1]),\n", "                'train_acc': float(base_history['train_acc'][-1]),\n", "                'val_acc': float(base_history['val_acc'][-1])\n", "            },\n", "            'inference_time_per_batch': float(base_mean_time),\n", "            'inference_time_per_sample': float(base_per_sample)\n", "        },\n", "        'improved_model': {\n", "            'parameters': int(improved_params),\n", "            'final_metrics': {\n", "                'train_loss': float(improved_history['train_loss'][-1]),\n", "                'val_loss': float(improved_history['val_loss'][-1]),\n", "                'train_acc': float(improved_history['train_acc'][-1]),\n", "                'val_acc': float(improved_history['val_acc'][-1])\n", "            },\n", "            'inference_time_per_batch': float(improved_mean_time),\n", "            'inference_time_per_sample': float(improved_per_sample)\n", "        }\n", "    },\n", "    'improvements': {\n", "        'parameter_increase': float((improved_params / base_params - 1) * 100),\n", "        'val_acc_improvement': float((improved_history['val_acc'][-1] - base_history['val_acc'][-1]) / base_history['val_acc'][-1] * 100),\n", "        'val_loss_improvement': float((base_history['val_loss'][-1] - improved_history['val_loss'][-1]) / base_history['val_loss'][-1] * 100),\n", "        'inference_time_increase': float((improved_mean_time / base_mean_time - 1) * 100)\n", "    }\n", "}\n", "\n", "# 保存到文件\n", "os.makedirs('../results', exist_ok=True)\n", "with open('../results/training_summary.json', 'w', encoding='utf-8') as f:\n", "    json.dump(training_summary, f, ensure_ascii=False, indent=2)\n", "\n", "print(\"训练总结已保存到 ../results/training_summary.json\")\n", "\n", "# 打印关键改进指标\n", "print(\"\\n=== 模型改进总结 ===\")\n", "print(f\"验证准确率提升: {training_summary['improvements']['val_acc_improvement']:.2f}%\")\n", "print(f\"验证损失降低: {training_summary['improvements']['val_loss_improvement']:.2f}%\")\n", "print(f\"参数量增加: {training_summary['improvements']['parameter_increase']:.1f}%\")\n", "print(f\"推理时间增加: {training_summary['improvements']['inference_time_increase']:.1f}%\")"]}, {"cell_type": "markdown", "id": "9642ca08", "metadata": {}, "source": ["## 8. 下一步\n", "\n", "1. **模型评估**: 使用 `03_evaluation_analysis.ipynb` 进行详细的模型评估\n", "2. **消融实验**: 使用 `04_ablation_study.ipynb` 分析各组件的贡献\n", "3. **超参数调优**: 尝试不同的学习率、批次大小等参数\n", "4. **模型部署**: 将最佳模型部署到生产环境\n", "\n", "### 训练建议\n", "\n", "1. **增加训练轮数**: 当前为了演示使用了较少的epoch，实际应用中可以增加到15-20轮\n", "2. **学习率调度**: 可以尝试不同的学习率调度策略\n", "3. **数据增强**: 可以考虑添加数据增强技术\n", "4. **模型集成**: 可以训练多个模型进行集成"]}], "metadata": {"kernelspec": {"display_name": "bert_sentiment_analysis", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}